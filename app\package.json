{"name": "vue-crash-2024", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "json-server --watch src/jobs.json --port 8000"}, "dependencies": {"axios": "^1.7.2", "echarts": "^5.6.0", "json-server": "^1.0.0-beta.1", "primeicons": "^7.0.0", "vue": "^3.4.29", "vue-router": "^4.4.0", "vue-spinner": "^1.0.4", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.4", "vite": "^5.3.1"}}