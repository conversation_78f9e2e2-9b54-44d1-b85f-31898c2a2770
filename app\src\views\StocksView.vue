<script setup>
import { RouterLink } from 'vue-router';
import { reactive, onMounted, onUnmounted,ref } from 'vue';
import PulseLoader from 'vue-spinner/src/PulseLoader.vue';
import axios from 'axios';
import * as echarts from 'echarts';
import { ContainerWithChildren } from 'postcss/lib/container';

const chartRef = ref(null)

let chart = null;
let chartOptions = null;
let option = {
  series: [
    {
      type: 'treemap',
      data: [
        {
          name: 'nodeA',
          value: 10,
          children: [
            {
              name: 'nodeAa',
              value: 4
            },
            {
              name: 'nodeAb',
              value: 6
            }
          ]
        },
        {
          name: 'nodeB',
          value: 20,
          children: [
            {
              name: 'nodeBa',
              value: 20,
              children: [
                {
                  name: 'nodeBa1',
                  value: 20
                }
              ]
            }
          ]
        }
      ]
    }
  ]
};

const fetchDataForm = reactive({
  industryCode: '0500',
  year: '2024',
});

const industryProps = reactive({
  industries: [],
  isLoading: true
});

const stocksByIndustry = reactive({
  stocks: [],
  isLoading: false
});

const handleFetchData = async () => {
  console.log("Fetch Data")

  stocksByIndustry.isLoading = true;
  const params = {
    industry_code: fetchDataForm.industryCode,
    year: fetchDataForm.year,
  }
  try {
    
    console.log(fetchDataForm);
    const response = await axios.get(`/api/get-stocks-by-industry`, { params });
    stocksByIndustry.stocks = response.data;
    
    chartOptions.series[0].name = fetchDataForm.industryCode;
    chartOptions.series[0].data = response.data;

    chart.setOption(chartOptions);
    
    console.log("after render")

  } catch (error) {
    console.error('Error fetching stocks', error);
  } finally {
    stocksByIndustry.isLoading = false;
  }
}
const initChart = async () => {
  chartOptions = {
      title: {
        text: 'Stocks Overview',
        subtext: 'Industrial code:',
        left: 'center'
      },
      series: [{
        name: fetchDataForm.industryCode,
        type: 'treemap',
        data: stocksByIndustry.stocks
      }]
    }
  if (chartRef.value && !chart) {
      chart = echarts.init(document.querySelector("#chart"))
      await chart.setOption(chartOptions)
    }
  }
const destroyChart = () => {
    if (chart) {
      try {
        if (chart.el && document.body.contains(chart.el)) {
          chart.destroy()
        }
      } catch (error) {
        console.warn('Chart destroy error:', error)
      } finally {
        chart = null
      }
    }
  }

onMounted(async () => {
  console.log("Mounted")
  try {
    const response = await axios.get(`/api/get-industries`);
    industryProps.industries = response.data;
    industryProps.isLoading = false;
  } catch (error) {
    console.error('Error fetching industries', error);
  } finally {
    industryProps.isLoading = false;
    await initChart();
  }
});
onUnmounted(async () => {
  await destroyChart();
});

</script>
<template>
  <div class="items-center justify-center bg-gray-100 pt-5">
    <div v-if="industryProps.isLoading" class="text-center text-gray-500 py-6">
      <PulseLoader />
    </div>

    <form v-else @submit.prevent="handleFetchData"
      class="flex p-6 bg-white rounded-lg shadow-lg items-center">
      <label class="px-4 py-2">Industry:</label>
      <select v-model="fetchDataForm.industryCode" id="industry" name="industry"
        class="border rounded px-4 py-2 w-50" required>
        <option v-for="industry in industryProps.industries" :key="industry.icb_code" :value="industry.icb_code">
          {{ industry.en_icb_name }}
        </option>
      </select>

      <label class="px-4 py-2">Year:</label>
      <select v-model="fetchDataForm.year" id="year" name="year" class="border rounded px-4 py-2 w-50" required>
        <option value="2024">2024</option>
        <option value="2023">2023</option>
        <option value="2022">2022</option>
        <option value="2021">2021</option>
      </select>

      <button class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 ml-4">
        Fetch Data
      </button>
    </form>
    <br>

    <div class="bg-white rounded-lg items-center justify-center h-full">

      <div id="chart" class="" ref="chartRef"></div>
    </div>
  </div>
</template>